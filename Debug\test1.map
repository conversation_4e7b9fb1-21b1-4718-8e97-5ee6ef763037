******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sat Aug  2 19:20:40 2025

OUTPUT FILE NAME:   <test1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00003451


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000057c0  0001a840  R  X
  SRAM                  20200000   00008000  000009b7  00007649  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000057c0   000057c0    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00003c10   00003c10    r-x .text
  00003cd0    00003cd0    00001a80   00001a80    r-- .rodata
  00005750    00005750    00000070   00000070    r-- .cinit
20200000    20200000    000007ba   00000000    rw-
  20200000    20200000    0000055d   00000000    rw- .bss
  20200560    20200560    0000025a   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00003c10     
                  000000c0    000001d0     oled.o (.text.OLED_ShowChar)
                  00000290    000001b8     motor.o (.text.Motor_Square_Corner_Control)
                  00000448    00000194     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000005dc    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000076e    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00000770    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  000008f8    0000016c     motor.o (.text.Set_PWM)
                  00000a64    0000015c     key.o (.text.Key_Scan_Debounce)
                  00000bc0    00000154     Ganway_Optimized.o (.text.Track_Basic_Control)
                  00000d14    0000014c     empty.o (.text.main)
                  00000e60    00000120     encoder.o (.text.GROUP1_IRQHandler)
                  00000f80    00000110     motor.o (.text.Motor_Smooth_Control)
                  00001090    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  0000119c    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000012a0    00000100     empty.o (.text.TIMG0_IRQHandler)
                  000013a0    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00001488    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  0000156c    000000e2     oled.o (.text.OLED_ShowNum)
                  0000164e    000000de     oled.o (.text.OLED_Init)
                  0000172c    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00001808    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000018e0    000000d0     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  000019b0    000000b8     Ganway_Optimized.o (.text.Analyze_Track_State)
                  00001a68    000000b8     motor.o (.text.Motor_PID_Control)
                  00001b20    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  00001bca    00000002     --HOLE-- [fill = 0]
                  00001bcc    000000a8     Ganway_Optimized.o (.text.Calculate_Line_Position)
                  00001c74    000000a8     motor.o (.text.Motor_Speed_Monitor)
                  00001d1c    0000009a     oled.o (.text.OLED_ShowSignedNum)
                  00001db6    0000009a     oled.o (.text.OLED_ShowString)
                  00001e50    00000090     oled.o (.text.OLED_DrawPoint)
                  00001ee0    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_0_init)
                  00001f6c    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001ff8    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00002084    00000084     oled.o (.text.OLED_Refresh)
                  00002108    00000084     Ganway_Optimized.o (.text.Way_Optimized)
                  0000218c    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00002210    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00002292    00000002     --HOLE-- [fill = 0]
                  00002294    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002310    00000074     motor.o (.text.Motor_Verify_Speed_Consistency)
                  00002384    00000074     Ganway_Optimized.o (.text.Track_Adaptive_Control)
                  000023f8    00000074     Ganway_Optimized.o (.text.Track_Init)
                  0000246c    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  000024e0    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00002552    00000002     --HOLE-- [fill = 0]
                  00002554    0000006c     oled.o (.text.OLED_WR_Byte)
                  000025c0    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  0000262c    00000068     Ganway_Optimized.o (.text.Handle_Lost_Line)
                  00002694    00000068     key.o (.text.Key_1)
                  000026fc    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00002764    00000062                            : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  000027c6    00000002     --HOLE-- [fill = 0]
                  000027c8    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  0000282a    00000002     --HOLE-- [fill = 0]
                  0000282c    00000060     oled.o (.text.OLED_Clear)
                  0000288c    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  000028ea    00000002     --HOLE-- [fill = 0]
                  000028ec    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00002944    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00002998    00000050     oled.o (.text.DL_I2C_startControllerTransfer)
                  000029e8    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00002a38    0000004c     empty.o (.text.DL_ADC12_initSingleSample)
                  00002a84    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00002ad0    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00002b1a    00000002     --HOLE-- [fill = 0]
                  00002b1c    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00002b66    0000004a     No_Mcu_Ganv_Grayscale_Sensor.o (.text.adc_getValue)
                  00002bb0    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00002bf8    00000048     oled.o (.text.OLED_DisplayTurn)
                  00002c40    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  00002c88    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00002cd0    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00002d14    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  00002d56    00000002     --HOLE-- [fill = 0]
                  00002d58    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00002d98    00000040     key.o (.text.Key)
                  00002dd8    00000040     Ganway_Optimized.o (.text.Track_Square_Corner_Control)
                  00002e18    00000040     bsp_usart.o (.text.UART0_IRQHandler)
                  00002e58    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00002e98    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00002ed4    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00002f10    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00002f4c    0000003c     Ganway_Optimized.o (.text.Track_PID_Control)
                  00002f88    0000003c     Ganway_Optimized.o (.text.Track_Weighted_Control)
                  00002fc4    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00003000    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  0000303c    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00003078    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  000030b2    00000002     --HOLE-- [fill = 0]
                  000030b4    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  000030ee    00000002     --HOLE-- [fill = 0]
                  000030f0    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00003128    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  0000315c    00000034     oled.o (.text.OLED_ColorTurn)
                  00003190    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  000031c4    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000031f8    00000030     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getMemResult)
                  00003228    00000030     oled.o (.text.OLED_Pow)
                  00003258    00000030     systick.o (.text.SysTick_Handler)
                  00003288    0000002c     empty.o (.text.__NVIC_ClearPendingIRQ)
                  000032b4    0000002c     empty.o (.text.__NVIC_EnableIRQ)
                  000032e0    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  0000330c    0000002c     libc.a : strncpy.c.obj (.text.strncpy)
                  00003338    00000028     Ganway_Optimized.o (.text.Calculate_Position_Error)
                  00003360    00000028     empty.o (.text.DL_Common_updateReg)
                  00003388    00000028     oled.o (.text.DL_Common_updateReg)
                  000033b0    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  000033d8    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00003400    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00003428    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00003450    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00003478    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  0000349e    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  000034c4    00000024     motor.o (.text.Left_Control)
                  000034e8    00000024     motor.o (.text.Left_Little_Control)
                  0000350c    00000024     motor.o (.text.Right_Control)
                  00003530    00000024     motor.o (.text.Right_Little_Control)
                  00003554    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00003578    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00003598    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  000035b8    00000020     motor.o (.text.Motor_Reset_Speed_Monitor)
                  000035d8    00000020     systick.o (.text.delay_ms)
                  000035f8    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  00003616    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00003634    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_startConversion)
                  00003650    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_stopConversion)
                  0000366c    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  00003688    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  000036a4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  000036c0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  000036dc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000036f8    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00003714    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00003730    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  0000374c    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00003768    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00003784    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  000037a0    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  000037b8    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  000037d0    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  000037e8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00003800    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00003818    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00003830    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00003848    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00003860    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  00003878    00000018     empty.o (.text.DL_GPIO_setPins)
                  00003890    00000018     motor.o (.text.DL_GPIO_setPins)
                  000038a8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  000038c0    00000018     empty.o (.text.DL_GPIO_togglePins)
                  000038d8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  000038f0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00003908    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00003920    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00003938    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00003950    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00003968    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00003980    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00003998    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000039b0    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000039c8    00000018     empty.o (.text.DL_Timer_startCounter)
                  000039e0    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  000039f8    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00003a10    00000018     Ganway_Optimized.o (.text.Handle_Intersection)
                  00003a28    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_disableConversions)
                  00003a3e    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_enableConversions)
                  00003a54    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00003a6a    00000016     encoder.o (.text.DL_GPIO_readPins)
                  00003a80    00000016     key.o (.text.DL_GPIO_readPins)
                  00003a96    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00003aac    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  00003ac0    00000014     empty.o (.text.DL_GPIO_clearPins)
                  00003ad4    00000014     motor.o (.text.DL_GPIO_clearPins)
                  00003ae8    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00003afc    00000014     oled.o (.text.DL_I2C_getControllerStatus)
                  00003b10    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00003b24    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00003b38    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00003b4c    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00003b60    00000014     bsp_usart.o (.text.DL_UART_receiveData)
                  00003b74    00000014     key.o (.text.Key_Init_Debounce)
                  00003b88    00000012     empty.o (.text.DL_Timer_getPendingInterrupt)
                  00003b9a    00000012     bsp_usart.o (.text.DL_UART_getPendingInterrupt)
                  00003bac    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00003bbe    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00003bd0    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00003be2    00000010     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getStatus)
                  00003bf2    00000002     --HOLE-- [fill = 0]
                  00003bf4    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00003c04    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00003c14    00000010     key.o (.text.Key_System_Tick_Inc)
                  00003c24    00000010     libc.a : copy_zero_init.c.obj (.text:decompress:ZI)
                  00003c34    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  00003c42    0000000e     libc.a : strcpy.c.obj (.text.strcpy)
                  00003c50    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00003c5e    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00003c6a    00000002     --HOLE-- [fill = 0]
                  00003c6c    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00003c78    0000000c     systick.o (.text.get_systicks)
                  00003c84    0000000c     Scheduler.o (.text.scheduler_init)
                  00003c90    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00003c9a    00000002     --HOLE-- [fill = 0]
                  00003c9c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00003ca4    00000006     libc.a : exit.c.obj (.text:abort)
                  00003caa    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00003cae    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00003cb2    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00003cb6    00000002     --HOLE-- [fill = 0]
                  00003cb8    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00003cc8    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00003ccc    00000004     --HOLE-- [fill = 0]

.cinit     0    00005750    00000070     
                  00005750    0000004a     (.cinit..data.load) [load image, compression = lzss]
                  0000579a    00000002     --HOLE-- [fill = 0]
                  0000579c    0000000c     (__TI_handler_table)
                  000057a8    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000057b0    00000010     (__TI_cinit_table)

.rodata    0    00003cd0    00001a80     
                  00003cd0    00000d5c     oled.o (.rodata.asc2_2412)
                  00004a2c    000005f0     oled.o (.rodata.asc2_1608)
                  0000501c    00000474     oled.o (.rodata.asc2_1206)
                  00005490    00000228     oled.o (.rodata.asc2_0806)
                  000056b8    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  000056e0    00000020     Ganway_Optimized.o (.rodata.sensor_weights)
                  00005700    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  00005714    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  0000571e    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00005720    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  00005728    00000008     ti_msp_dl_config.o (.rodata.gPWM_0Config)
                  00005730    00000008     motor.o (.rodata.str1.5850567729483738290.1)
                  00005738    00000006     motor.o (.rodata.str1.10718775090649846465.1)
                  0000573e    00000003     ti_msp_dl_config.o (.rodata.gPWM_0ClockConfig)
                  00005741    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  00005744    00000003     empty.o (.rodata.str1.9517790425240694019.1)
                  00005747    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00005749    00000007     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    0000055d     UNINITIALIZED
                  20200000    00000480     (.common:OLED_GRAM)
                  20200480    000000bc     (.common:gPWM_0Backup)
                  2020053c    00000004     (.common:Flag_stop)
                  20200540    00000004     (.common:Flag_stop1)
                  20200544    00000004     (.common:Get_Encoder_countA)
                  20200548    00000004     (.common:Get_Encoder_countB)
                  2020054c    00000004     (.common:encoderA_cnt)
                  20200550    00000004     (.common:encoderB_cnt)
                  20200554    00000004     (.common:gpio_interrup1)
                  20200558    00000004     (.common:gpio_interrup2)
                  2020055c    00000001     (.common:task_num)

.data      0    20200560    0000025a     UNINITIALIZED
                  20200560    00000100     empty.o (.data.rx_buff)
                  20200660    00000080     bsp_usart.o (.data.uart_rx_buffer)
                  202006e0    00000038     motor.o (.data.speed_monitor)
                  20200718    00000024     motor.o (.data.motor_pid)
                  2020073c    00000020     Ganway_Optimized.o (.data.track_ctrl)
                  2020075c    00000010     empty.o (.data.Anolog)
                  2020076c    00000010     empty.o (.data.black)
                  2020077c    00000010     empty.o (.data.white)
                  2020078c    0000000c     key.o (.data.key1_ctrl)
                  20200798    00000008     systick.o (.data.systicks)
                  202007a0    00000004     empty.o (.data.D_Num)
                  202007a4    00000004     motor.o (.data.Motor_Square_Corner_Control.last_turn_direction)
                  202007a8    00000004     motor.o (.data.Motor_Square_Corner_Control.sharp_turn_counter)
                  202007ac    00000004     empty.o (.data.Run)
                  202007b0    00000004     systick.o (.data.delay_times)
                  202007b4    00000004     key.o (.data.system_tick_ms)
                  202007b8    00000001     bsp_usart.o (.data.uart_rx_index)
                  202007b9    00000001     bsp_usart.o (.data.uart_rx_ticks)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               2772    96        188    
       empty.o                          902     3         328    
       startup_mspm0g350x_ticlang.o     8       192       0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3682    291       516    
                                                                 
    .\app\
       motor.o                          1764    14        100    
       Ganway_Optimized.o               1408    32        32     
       No_Mcu_Ganv_Grayscale_Sensor.o   1414    0         0      
       key.o                            574     0         16     
       encoder.o                        362     0         16     
       Scheduler.o                      12      0         1      
    +--+--------------------------------+-------+---------+---------+
       Total:                           5534    46        165    
                                                                 
    .\app\OLED\
       oled.o                           2012    6632      1152   
    +--+--------------------------------+-------+---------+---------+
       Total:                           2012    6632      1152   
                                                                 
    .\bsp\
       bsp_usart.o                      102     0         130    
       systick.o                        92      0         12     
    +--+--------------------------------+-------+---------+---------+
       Total:                           194     0         142    
                                                                 
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                       588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o     288     0         0      
       dl_i2c.o                         132     0         0      
       dl_uart.o                        90      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1172    0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj       124     0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       strncpy.c.obj                    44      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       copy_zero_init.c.obj             16      0         0      
       memset16.S.obj                   14      0         0      
       strcpy.c.obj                     14      0         0      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           374     0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     402     0         0      
       divdf3.S.obj                     268     0         0      
       muldf3.S.obj                     228     0         0      
       comparedf2.c.obj                 220     0         0      
       addsf3.S.obj                     216     0         0      
       mulsf3.S.obj                     140     0         0      
       divsf3.S.obj                     130     0         0      
       comparesf2.S.obj                 118     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       fixdfsi.S.obj                    74      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       fixsfsi.S.obj                    56      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsisf.S.obj                40      0         0      
       floatunsidf.S.obj                36      0         0      
       aeabi_memset.S.obj               12      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 2       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           2372    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       110       0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     15344   7079      2487   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000057b0 records: 2, size/record: 8, table size: 16
	.data: load addr=00005750, load size=0000004a bytes, run addr=20200560, run size=0000025a bytes, compression=lzss
	.bss: load addr=000057a8, load size=00000008 bytes, run addr=20200000, run size=0000055d bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 0000579c records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00003451     00003cb8     00003cb2   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[1 trampolines]
[1 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00003cab  ADC0_IRQHandler                      
00003cab  ADC1_IRQHandler                      
00003cab  AES_IRQHandler                       
000019b1  Analyze_Track_State                  
2020075c  Anolog                               
00003cae  C$$EXIT                              
00003cab  CANFD0_IRQHandler                    
00001bcd  Calculate_Line_Position              
00003339  Calculate_Position_Error             
00003cab  DAC0_IRQHandler                      
00002d59  DL_ADC12_setClockConfig              
00003c91  DL_Common_delayCycles                
0000288d  DL_I2C_fillControllerTXFIFO          
0000349f  DL_I2C_setClockConfig                
0000172d  DL_SYSCTL_configSYSPLL               
00002cd1  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
0000119d  DL_Timer_initFourCCPWMMode           
000013a1  DL_Timer_initTimerMode               
0000374d  DL_Timer_setCaptCompUpdateMethod     
000039b1  DL_Timer_setCaptureCompareOutCtl     
00003c05  DL_Timer_setCaptureCompareValue      
00003769  DL_Timer_setClockConfig              
00002bb1  DL_UART_init                         
00003bad  DL_UART_setClockConfig               
00003cab  DMA_IRQHandler                       
202007a0  D_Num                                
00003cab  Default_Handler                      
2020053c  Flag_stop                            
20200540  Flag_stop1                           
00003cab  GROUP0_IRQHandler                    
00000e61  GROUP1_IRQHandler                    
000018e1  Get_Analog_value                     
00002f11  Get_Anolog_Value                     
00003c35  Get_Digtal_For_User                  
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
00003caf  HOSTexit                             
00003a11  Handle_Intersection                  
0000262d  Handle_Lost_Line                     
00003cab  HardFault_Handler                    
00003cab  I2C0_IRQHandler                      
00003cab  I2C1_IRQHandler                      
00002d99  Key                                  
00002695  Key_1                                
00003b75  Key_Init_Debounce                    
00000a65  Key_Scan_Debounce                    
00003c15  Key_System_Tick_Inc                  
000034c5  Left_Control                         
000034e9  Left_Little_Control                  
00001a69  Motor_PID_Control                    
000035b9  Motor_Reset_Speed_Monitor            
00000f81  Motor_Smooth_Control                 
00001c75  Motor_Speed_Monitor                  
00000291  Motor_Square_Corner_Control          
00002311  Motor_Verify_Speed_Consistency       
00003cab  NMI_Handler                          
00000771  No_MCU_Ganv_Sensor_Init              
000024e1  No_MCU_Ganv_Sensor_Init_Frist        
00002d15  No_Mcu_Ganv_Sensor_Task_Without_tick 
0000282d  OLED_Clear                           
0000315d  OLED_ColorTurn                       
00002bf9  OLED_DisplayTurn                     
00001e51  OLED_DrawPoint                       
20200000  OLED_GRAM                            
0000164f  OLED_Init                            
00003229  OLED_Pow                             
00002085  OLED_Refresh                         
000000c1  OLED_ShowChar                        
0000156d  OLED_ShowNum                         
00001d1d  OLED_ShowSignedNum                   
00001db7  OLED_ShowString                      
00002555  OLED_WR_Byte                         
00003cab  PendSV_Handler                       
00003cab  RTC_IRQHandler                       
00003cb3  Reset_Handler                        
0000350d  Right_Control                        
00003531  Right_Little_Control                 
202007ac  Run                                  
00003cab  SPI0_IRQHandler                      
00003cab  SPI1_IRQHandler                      
00003cab  SVC_Handler                          
00002c41  SYSCFG_DL_ADC12_0_init               
00000449  SYSCFG_DL_GPIO_init                  
000028ed  SYSCFG_DL_I2C_OLED_init              
00001ee1  SYSCFG_DL_PWM_0_init                 
00002c89  SYSCFG_DL_SYSCTL_init                
00003c5f  SYSCFG_DL_SYSTICK_init               
00003191  SYSCFG_DL_TIMER_0_init               
00002945  SYSCFG_DL_UART_0_init                
000031c5  SYSCFG_DL_init                       
00001f6d  SYSCFG_DL_initPower                  
000008f9  Set_PWM                              
00003259  SysTick_Handler                      
00003cab  TIMA0_IRQHandler                     
00003cab  TIMA1_IRQHandler                     
000012a1  TIMG0_IRQHandler                     
00003cab  TIMG12_IRQHandler                    
00003cab  TIMG6_IRQHandler                     
00003cab  TIMG7_IRQHandler                     
00003cab  TIMG8_IRQHandler                     
00003bbf  TI_memcpy_small                      
00003c51  TI_memset_small                      
00002385  Track_Adaptive_Control               
00000bc1  Track_Basic_Control                  
000023f9  Track_Init                           
00002f4d  Track_PID_Control                    
00002dd9  Track_Square_Corner_Control          
00002f89  Track_Weighted_Control               
00002e19  UART0_IRQHandler                     
00003cab  UART1_IRQHandler                     
00003cab  UART2_IRQHandler                     
00003cab  UART3_IRQHandler                     
00002109  Way_Optimized                        
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
000057b0  __TI_CINIT_Base                      
000057c0  __TI_CINIT_Limit                     
000057c0  __TI_CINIT_Warm                      
0000579c  __TI_Handler_Table_Base              
000057a8  __TI_Handler_Table_Limit             
0000303d  __TI_auto_init_nobinit_nopinit       
00002295  __TI_decompress_lzss                 
00003bd1  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00003c25  __TI_zero_init                       
000005e7  __adddf3                             
00001813  __addsf3                             
00002b1d  __aeabi_d2iz                         
000005e7  __aeabi_dadd                         
00002765  __aeabi_dcmpeq                       
000027a1  __aeabi_dcmpge                       
000027b5  __aeabi_dcmpgt                       
0000278d  __aeabi_dcmple                       
00002779  __aeabi_dcmplt                       
00001091  __aeabi_ddiv                         
00001489  __aeabi_dmul                         
000005dd  __aeabi_dsub                         
000030f1  __aeabi_f2iz                         
00001813  __aeabi_fadd                         
000027c9  __aeabi_fcmpeq                       
00002805  __aeabi_fcmpge                       
00002819  __aeabi_fcmpgt                       
000027f1  __aeabi_fcmple                       
000027dd  __aeabi_fcmplt                       
00002211  __aeabi_fdiv                         
00001ff9  __aeabi_fmul                         
00001809  __aeabi_fsub                         
000032e1  __aeabi_i2d                          
00002fc5  __aeabi_i2f                          
0000076f  __aeabi_idiv0                        
00003c6d  __aeabi_memclr                       
00003c6d  __aeabi_memclr4                      
00003c6d  __aeabi_memclr8                      
00003c9d  __aeabi_memcpy                       
00003c9d  __aeabi_memcpy4                      
00003c9d  __aeabi_memcpy8                      
00003555  __aeabi_ui2d                         
00003429  __aeabi_ui2f                         
00002e59  __aeabi_uidiv                        
00002e59  __aeabi_uidivmod                     
ffffffff  __binit__                            
000026fd  __cmpdf2                             
00003079  __cmpsf2                             
00001091  __divdf3                             
00002211  __divsf3                             
000026fd  __eqdf2                              
00003079  __eqsf2                              
00002b1d  __fixdfsi                            
000030f1  __fixsfsi                            
000032e1  __floatsidf                          
00002fc5  __floatsisf                          
00003555  __floatunsidf                        
00003429  __floatunsisf                        
0000246d  __gedf2                              
00003001  __gesf2                              
0000246d  __gtdf2                              
00003001  __gtsf2                              
000026fd  __ledf2                              
00003079  __lesf2                              
000026fd  __ltdf2                              
00003079  __ltsf2                              
UNDEFED   __mpu_init                           
00001489  __muldf3                             
000030b5  __muldsi3                            
00001ff9  __mulsf3                             
000026fd  __nedf2                              
00003079  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000005dd  __subdf3                             
00001809  __subsf3                             
00003451  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00003cc9  _system_pre_init                     
00003ca5  abort                                
00002b67  adc_getValue                         
00005490  asc2_0806                            
0000501c  asc2_1206                            
00004a2c  asc2_1608                            
00003cd0  asc2_2412                            
ffffffff  binit                                
2020076c  black                                
000025c1  convertAnalogToDigital               
000035d9  delay_ms                             
202007b0  delay_times                          
2020054c  encoderA_cnt                         
20200550  encoderB_cnt                         
20200480  gPWM_0Backup                         
00003c79  get_systicks                         
20200554  gpio_interrup1                       
20200558  gpio_interrup2                       
00000000  interruptVectors                     
2020078c  key1_ctrl                            
00000d15  main                                 
20200718  motor_pid                            
00001b21  normalizeAnalogValues                
20200560  rx_buff                              
00003c85  scheduler_init                       
00003c43  strcpy                               
0000330d  strncpy                              
2020055c  task_num                             
2020073c  track_ctrl                           
20200660  uart_rx_buffer                       
202007b8  uart_rx_index                        
202007b9  uart_rx_ticks                        
2020077c  white                                


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  OLED_ShowChar                        
00000200  __STACK_SIZE                         
00000291  Motor_Square_Corner_Control          
00000449  SYSCFG_DL_GPIO_init                  
000005dd  __aeabi_dsub                         
000005dd  __subdf3                             
000005e7  __adddf3                             
000005e7  __aeabi_dadd                         
0000076f  __aeabi_idiv0                        
00000771  No_MCU_Ganv_Sensor_Init              
000008f9  Set_PWM                              
00000a65  Key_Scan_Debounce                    
00000bc1  Track_Basic_Control                  
00000d15  main                                 
00000e61  GROUP1_IRQHandler                    
00000f81  Motor_Smooth_Control                 
00001091  __aeabi_ddiv                         
00001091  __divdf3                             
0000119d  DL_Timer_initFourCCPWMMode           
000012a1  TIMG0_IRQHandler                     
000013a1  DL_Timer_initTimerMode               
00001489  __aeabi_dmul                         
00001489  __muldf3                             
0000156d  OLED_ShowNum                         
0000164f  OLED_Init                            
0000172d  DL_SYSCTL_configSYSPLL               
00001809  __aeabi_fsub                         
00001809  __subsf3                             
00001813  __addsf3                             
00001813  __aeabi_fadd                         
000018e1  Get_Analog_value                     
000019b1  Analyze_Track_State                  
00001a69  Motor_PID_Control                    
00001b21  normalizeAnalogValues                
00001bcd  Calculate_Line_Position              
00001c75  Motor_Speed_Monitor                  
00001d1d  OLED_ShowSignedNum                   
00001db7  OLED_ShowString                      
00001e51  OLED_DrawPoint                       
00001ee1  SYSCFG_DL_PWM_0_init                 
00001f6d  SYSCFG_DL_initPower                  
00001ff9  __aeabi_fmul                         
00001ff9  __mulsf3                             
00002085  OLED_Refresh                         
00002109  Way_Optimized                        
00002211  __aeabi_fdiv                         
00002211  __divsf3                             
00002295  __TI_decompress_lzss                 
00002311  Motor_Verify_Speed_Consistency       
00002385  Track_Adaptive_Control               
000023f9  Track_Init                           
0000246d  __gedf2                              
0000246d  __gtdf2                              
000024e1  No_MCU_Ganv_Sensor_Init_Frist        
00002555  OLED_WR_Byte                         
000025c1  convertAnalogToDigital               
0000262d  Handle_Lost_Line                     
00002695  Key_1                                
000026fd  __cmpdf2                             
000026fd  __eqdf2                              
000026fd  __ledf2                              
000026fd  __ltdf2                              
000026fd  __nedf2                              
00002765  __aeabi_dcmpeq                       
00002779  __aeabi_dcmplt                       
0000278d  __aeabi_dcmple                       
000027a1  __aeabi_dcmpge                       
000027b5  __aeabi_dcmpgt                       
000027c9  __aeabi_fcmpeq                       
000027dd  __aeabi_fcmplt                       
000027f1  __aeabi_fcmple                       
00002805  __aeabi_fcmpge                       
00002819  __aeabi_fcmpgt                       
0000282d  OLED_Clear                           
0000288d  DL_I2C_fillControllerTXFIFO          
000028ed  SYSCFG_DL_I2C_OLED_init              
00002945  SYSCFG_DL_UART_0_init                
00002b1d  __aeabi_d2iz                         
00002b1d  __fixdfsi                            
00002b67  adc_getValue                         
00002bb1  DL_UART_init                         
00002bf9  OLED_DisplayTurn                     
00002c41  SYSCFG_DL_ADC12_0_init               
00002c89  SYSCFG_DL_SYSCTL_init                
00002cd1  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00002d15  No_Mcu_Ganv_Sensor_Task_Without_tick 
00002d59  DL_ADC12_setClockConfig              
00002d99  Key                                  
00002dd9  Track_Square_Corner_Control          
00002e19  UART0_IRQHandler                     
00002e59  __aeabi_uidiv                        
00002e59  __aeabi_uidivmod                     
00002f11  Get_Anolog_Value                     
00002f4d  Track_PID_Control                    
00002f89  Track_Weighted_Control               
00002fc5  __aeabi_i2f                          
00002fc5  __floatsisf                          
00003001  __gesf2                              
00003001  __gtsf2                              
0000303d  __TI_auto_init_nobinit_nopinit       
00003079  __cmpsf2                             
00003079  __eqsf2                              
00003079  __lesf2                              
00003079  __ltsf2                              
00003079  __nesf2                              
000030b5  __muldsi3                            
000030f1  __aeabi_f2iz                         
000030f1  __fixsfsi                            
0000315d  OLED_ColorTurn                       
00003191  SYSCFG_DL_TIMER_0_init               
000031c5  SYSCFG_DL_init                       
00003229  OLED_Pow                             
00003259  SysTick_Handler                      
000032e1  __aeabi_i2d                          
000032e1  __floatsidf                          
0000330d  strncpy                              
00003339  Calculate_Position_Error             
00003429  __aeabi_ui2f                         
00003429  __floatunsisf                        
00003451  _c_int00_noargs                      
0000349f  DL_I2C_setClockConfig                
000034c5  Left_Control                         
000034e9  Left_Little_Control                  
0000350d  Right_Control                        
00003531  Right_Little_Control                 
00003555  __aeabi_ui2d                         
00003555  __floatunsidf                        
000035b9  Motor_Reset_Speed_Monitor            
000035d9  delay_ms                             
0000374d  DL_Timer_setCaptCompUpdateMethod     
00003769  DL_Timer_setClockConfig              
000039b1  DL_Timer_setCaptureCompareOutCtl     
00003a11  Handle_Intersection                  
00003b75  Key_Init_Debounce                    
00003bad  DL_UART_setClockConfig               
00003bbf  TI_memcpy_small                      
00003bd1  __TI_decompress_none                 
00003c05  DL_Timer_setCaptureCompareValue      
00003c15  Key_System_Tick_Inc                  
00003c25  __TI_zero_init                       
00003c35  Get_Digtal_For_User                  
00003c43  strcpy                               
00003c51  TI_memset_small                      
00003c5f  SYSCFG_DL_SYSTICK_init               
00003c6d  __aeabi_memclr                       
00003c6d  __aeabi_memclr4                      
00003c6d  __aeabi_memclr8                      
00003c79  get_systicks                         
00003c85  scheduler_init                       
00003c91  DL_Common_delayCycles                
00003c9d  __aeabi_memcpy                       
00003c9d  __aeabi_memcpy4                      
00003c9d  __aeabi_memcpy8                      
00003ca5  abort                                
00003cab  ADC0_IRQHandler                      
00003cab  ADC1_IRQHandler                      
00003cab  AES_IRQHandler                       
00003cab  CANFD0_IRQHandler                    
00003cab  DAC0_IRQHandler                      
00003cab  DMA_IRQHandler                       
00003cab  Default_Handler                      
00003cab  GROUP0_IRQHandler                    
00003cab  HardFault_Handler                    
00003cab  I2C0_IRQHandler                      
00003cab  I2C1_IRQHandler                      
00003cab  NMI_Handler                          
00003cab  PendSV_Handler                       
00003cab  RTC_IRQHandler                       
00003cab  SPI0_IRQHandler                      
00003cab  SPI1_IRQHandler                      
00003cab  SVC_Handler                          
00003cab  TIMA0_IRQHandler                     
00003cab  TIMA1_IRQHandler                     
00003cab  TIMG12_IRQHandler                    
00003cab  TIMG6_IRQHandler                     
00003cab  TIMG7_IRQHandler                     
00003cab  TIMG8_IRQHandler                     
00003cab  UART1_IRQHandler                     
00003cab  UART2_IRQHandler                     
00003cab  UART3_IRQHandler                     
00003cae  C$$EXIT                              
00003caf  HOSTexit                             
00003cb3  Reset_Handler                        
00003cc9  _system_pre_init                     
00003cd0  asc2_2412                            
00004a2c  asc2_1608                            
0000501c  asc2_1206                            
00005490  asc2_0806                            
0000579c  __TI_Handler_Table_Base              
000057a8  __TI_Handler_Table_Limit             
000057b0  __TI_CINIT_Base                      
000057c0  __TI_CINIT_Limit                     
000057c0  __TI_CINIT_Warm                      
20200000  OLED_GRAM                            
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200480  gPWM_0Backup                         
2020053c  Flag_stop                            
20200540  Flag_stop1                           
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
2020054c  encoderA_cnt                         
20200550  encoderB_cnt                         
20200554  gpio_interrup1                       
20200558  gpio_interrup2                       
2020055c  task_num                             
20200560  rx_buff                              
20200660  uart_rx_buffer                       
20200718  motor_pid                            
2020073c  track_ctrl                           
2020075c  Anolog                               
2020076c  black                                
2020077c  white                                
2020078c  key1_ctrl                            
202007a0  D_Num                                
202007ac  Run                                  
202007b0  delay_times                          
202007b8  uart_rx_index                        
202007b9  uart_rx_ticks                        
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[241 symbols]

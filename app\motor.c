#include "motor.h"
#include "Track_Config.h"  // 引入配置文件
#include "Ganway_Optimized.h"  // 引入Track_Control_t定义

// 全局PID参数初始化
Motor_PID_t motor_pid = {
    .base_speed = TRACK_BASE_SPEED_NORMAL,
    .max_speed = TRACK_MAX_SPEED,
    .min_speed = TRACK_MIN_SPEED,
    .kp = TRACK_PID_KP,
    .ki = TRACK_PID_KI,
    .kd = TRACK_PID_KD,
    .last_error = 0,
    .integral = 0,
    .max_integral = TRACK_PID_MAX_INTEGRAL
};

void Set_PWM(int pwmA,int pwmB)
{
    // 限制PWM值范围，防止过大的值损坏电机
    if(pwmA > MAX_SPEED) pwmA = MAX_SPEED;
    if(pwmA < -MAX_SPEED) pwmA = -MAX_SPEED;
    if(pwmB > MAX_SPEED) pwmB = MAX_SPEED;
    if(pwmB < -MAX_SPEED) pwmB = -MAX_SPEED;

    // 速度监控（在实际设置PWM前记录）
    Motor_Speed_Monitor(pwmA, pwmB, "Set_PWM");

	 if(pwmA>0)
    {
        DL_GPIO_setPins(AIN_PORT,AIN_AIN2_PIN);
        DL_GPIO_clearPins(AIN_PORT,AIN_AIN1_PIN);
		DL_Timer_setCaptureCompareValue(PWM_0_INST,ABS(pwmA),GPIO_PWM_0_C0_IDX);
    }
    else
    {
        DL_GPIO_setPins(AIN_PORT,AIN_AIN1_PIN);
        DL_GPIO_clearPins(AIN_PORT,AIN_AIN2_PIN);
		DL_Timer_setCaptureCompareValue(PWM_0_INST,ABS(pwmA),GPIO_PWM_0_C0_IDX);
    }
    if(pwmB>0)
    {
		DL_GPIO_setPins(BIN_PORT,BIN_BIN2_PIN);
        DL_GPIO_clearPins(BIN_PORT,BIN_BIN1_PIN);
        DL_Timer_setCaptureCompareValue(PWM_0_INST,ABS(pwmB),GPIO_PWM_0_C1_IDX);
    }
    else
    {
		DL_GPIO_setPins(BIN_PORT,BIN_BIN1_PIN);
        DL_GPIO_clearPins(BIN_PORT,BIN_BIN2_PIN);
		 DL_Timer_setCaptureCompareValue(PWM_0_INST,ABS(pwmB),GPIO_PWM_0_C1_IDX);
    }
}

// 传统控制函数（重构为统一速度控制，解决转向后速度变快问题）
void Right_Control(void)
{
    // 使用减速差速策略：快轮保持base_speed，慢轮减速
    // 这确保转向时任何轮子都不超过直行速度
    extern Track_Control_t track_ctrl;  // 引用全局控制变量
    int base = track_ctrl.base_speed;
    int diff = TRACK_TURN_DIFF;
    Set_PWM(base - diff, base);  // 左轮减速，右轮保持base_speed
}

void Left_Control(void)
{
    // 左转：右轮保持base_speed，左轮减速
    extern Track_Control_t track_ctrl;
    int base = track_ctrl.base_speed;
    int diff = TRACK_TURN_DIFF;
    Set_PWM(base, base - diff);  // 左轮保持base_speed，右轮减速
}

void Left_Large_Control(void)
{
    // 大幅左转：使用更大的差速，但仍保持速度一致性原则
    extern Track_Control_t track_ctrl;
    int base = track_ctrl.base_speed;
    int diff = TRACK_TURN_DIFF_LARGE;
    Set_PWM(base, base - diff);  // 避免使用反向，保持控制稳定
}

void Right_Large_Control(void)
{
    // 大幅右转：使用更大的差速
    extern Track_Control_t track_ctrl;
    int base = track_ctrl.base_speed;
    int diff = TRACK_TURN_DIFF_LARGE;
    Set_PWM(base - diff, base);
}

void Left_Little_Control(void)
{
    // 微调左转：使用较小的差速，提供精细控制
    extern Track_Control_t track_ctrl;
    int base = track_ctrl.base_speed;
    int diff = TRACK_TURN_DIFF_SMALL;
    Set_PWM(base, base - diff);
}

void Right_Little_Control(void)
{
    // 微调右转：使用较小的差速
    extern Track_Control_t track_ctrl;
    int base = track_ctrl.base_speed;
    int diff = TRACK_TURN_DIFF_SMALL;
    Set_PWM(base - diff, base);
}

// 新增：平滑控制函数（优化版，确保速度一致性）
void Motor_Smooth_Control(int error, int base_speed)
{
    int left_speed, right_speed;
    int turn_adjustment;

    // 根据误差计算转向调整量（使用优化后的系数）
    turn_adjustment = error * TRACK_SMOOTH_FACTOR;

    // 限制转向调整量
    if(turn_adjustment > TRACK_MAX_TURN_DIFF) turn_adjustment = TRACK_MAX_TURN_DIFF;
    if(turn_adjustment < -TRACK_MAX_TURN_DIFF) turn_adjustment = -TRACK_MAX_TURN_DIFF;

    // 使用减速差速策略，确保任何轮子都不超过base_speed
    if(turn_adjustment > 0) {
        // 右转：左轮保持base_speed，右轮减速
        left_speed = base_speed;
        right_speed = base_speed - abs(turn_adjustment);
    } else if(turn_adjustment < 0) {
        // 左转：右轮保持base_speed，左轮减速
        left_speed = base_speed - abs(turn_adjustment);
        right_speed = base_speed;
    } else {
        // 直行：两轮相等
        left_speed = base_speed;
        right_speed = base_speed;
    }

    // 确保速度不低于最小值
    if(left_speed < TRACK_MIN_SPEED && left_speed > 0) left_speed = TRACK_MIN_SPEED;
    if(right_speed < TRACK_MIN_SPEED && right_speed > 0) right_speed = TRACK_MIN_SPEED;

    // 速度上限保护：确保任何轮子都不超过base_speed
    if(left_speed > base_speed) left_speed = base_speed;
    if(right_speed > base_speed) right_speed = base_speed;

    // 最终安全限制
    if(left_speed > TRACK_MAX_SPEED) left_speed = TRACK_MAX_SPEED;
    if(right_speed > TRACK_MAX_SPEED) right_speed = TRACK_MAX_SPEED;

    Set_PWM(left_speed, right_speed);
}

// 新增：PID控制函数
void Motor_PID_Control(int error, int base_speed)
{
    int derivative, output;

    // 积分计算（带限幅）
    motor_pid.integral += error;
    if(motor_pid.integral > motor_pid.max_integral)
        motor_pid.integral = motor_pid.max_integral;
    if(motor_pid.integral < -motor_pid.max_integral)
        motor_pid.integral = -motor_pid.max_integral;

    // 微分计算
    derivative = error - motor_pid.last_error;

    // PID输出计算
    output = (int)(motor_pid.kp * error +
                   motor_pid.ki * motor_pid.integral +
                   motor_pid.kd * derivative);

    // 更新上次误差
    motor_pid.last_error = error;

    // 使用PID输出进行电机控制
    Motor_Smooth_Control(output, base_speed);
}

// 新增：停止电机函数
void Motor_Stop(void)
{
    Set_PWM(0, 0);
}

// 新增：正方形轨道专用直角转弯控制（优化版：一轮停止，一轮转动）
void Motor_Square_Corner_Control(int error, int base_speed)
{
    int left_speed, right_speed;
    int turn_adjustment;
    static int sharp_turn_counter = 0;  // 连续检测计数器
    static int last_turn_direction = 0; // 上次转向方向

    // 使用更保守的转向调整量，专门针对直角转弯优化
    turn_adjustment = error * TRACK_SMOOTH_FACTOR;

    // 限制转向调整量，使用正方形轨道专用参数
    if(turn_adjustment > TRACK_SQUARE_TURN_DIFF) turn_adjustment = TRACK_SQUARE_TURN_DIFF;
    if(turn_adjustment < -TRACK_SQUARE_TURN_DIFF) turn_adjustment = -TRACK_SQUARE_TURN_DIFF;

    // 检测是否为直角转弯（连续大偏差）
    if(abs(error) > TRACK_SQUARE_SHARP_TURN_THRESHOLD) {  // 检测到较大偏差
        sharp_turn_counter++;

        // 连续检测到大偏差，判定为直角转弯
        if(sharp_turn_counter > TRACK_SQUARE_SHARP_TURN_COUNT) {
            // 使用直角转弯专用控制：一轮反转，一轮正转（最大差速比）
            int turn_speed = base_speed * TRACK_SQUARE_CORNER_RATIO;  // 转弯速度
            int reverse_speed = turn_speed * 0.6f;  // 反转速度（稍小以保持控制）

            if(turn_adjustment > 0) {
                // 右转：左轮正转，右轮反转
                left_speed = turn_speed;
                right_speed = -reverse_speed;  // 反转以获得最大转弯力矩
                last_turn_direction = 1;  // 记录右转
            } else if(turn_adjustment < 0) {
                // 左转：右轮正转，左轮反转
                left_speed = -reverse_speed;  // 反转以获得最大转弯力矩
                right_speed = turn_speed;
                last_turn_direction = -1; // 记录左转
            } else {
                // 直行（但仍在直角区域）
                left_speed = turn_speed;
                right_speed = turn_speed;
            }
        } else {
            // 刚开始检测到偏差，使用渐进式控制
            base_speed = base_speed * 0.9f;  // 轻微降速

            if(turn_adjustment > 0) {
                // 右转：左轮保持速度，右轮减速
                left_speed = base_speed;
                right_speed = base_speed - TRACK_SQUARE_TURN_DIFF;
            } else if(turn_adjustment < 0) {
                // 左转：右轮保持速度，左轮减速
                left_speed = base_speed - TRACK_SQUARE_TURN_DIFF;
                right_speed = base_speed;
            } else {
                // 直行
                left_speed = base_speed;
                right_speed = base_speed;
            }
        }
    } else {
        // 小偏差或回到正常状态
        if(sharp_turn_counter > 0) {
            sharp_turn_counter--;  // 逐渐减少计数器
        }

        // 如果刚从直角转弯恢复，使用平滑过渡
        if(sharp_turn_counter == 0 && last_turn_direction != 0) {
            // 平滑过渡到正常控制
            Motor_Smooth_Control(error, base_speed * 0.95f);
            last_turn_direction = 0;  // 重置转向记录
            return;
        } else {
            // 正常的小偏差控制
            Motor_Smooth_Control(error, base_speed);
            return;
        }
    }

    // 确保速度不为负值（安全检查）
    if(left_speed < 0) left_speed = 0;
    if(right_speed < 0) right_speed = 0;

    // 安全限制
    if(left_speed > TRACK_MAX_SPEED) left_speed = TRACK_MAX_SPEED;
    if(right_speed > TRACK_MAX_SPEED) right_speed = TRACK_MAX_SPEED;

    Set_PWM(left_speed, right_speed);
}

// 速度监控相关变量
static struct {
    int last_pwmA;
    int last_pwmB;
    int max_pwmA_recorded;
    int max_pwmB_recorded;
    int base_speed_reference;
    bool speed_violation_detected;
    char last_source[32];
} speed_monitor = {0};

// 速度监控函数
void Motor_Speed_Monitor(int pwmA, int pwmB, const char* source)
{
    // 记录当前PWM值
    speed_monitor.last_pwmA = pwmA;
    speed_monitor.last_pwmB = pwmB;

    // 更新最大值记录
    if(abs(pwmA) > speed_monitor.max_pwmA_recorded) {
        speed_monitor.max_pwmA_recorded = abs(pwmA);
    }
    if(abs(pwmB) > speed_monitor.max_pwmB_recorded) {
        speed_monitor.max_pwmB_recorded = abs(pwmB);
    }

    // 记录调用来源
    strncpy(speed_monitor.last_source, source, sizeof(speed_monitor.last_source) - 1);
    speed_monitor.last_source[sizeof(speed_monitor.last_source) - 1] = '\0';

    // 获取当前基础速度作为参考
    extern Track_Control_t track_ctrl;
    speed_monitor.base_speed_reference = track_ctrl.base_speed;

    // 检查速度一致性违规
    if(abs(pwmA) > speed_monitor.base_speed_reference ||
       abs(pwmB) > speed_monitor.base_speed_reference) {
        speed_monitor.speed_violation_detected = true;

        #if TRACK_DEBUG_ENABLE
        // 可以在这里添加调试输出
        // printf("Speed violation: PWM(%d,%d) > base_speed(%d) from %s\n",
        //        pwmA, pwmB, speed_monitor.base_speed_reference, source);
        #endif
    }
}

// 速度一致性验证函数
bool Motor_Verify_Speed_Consistency(void)
{
    extern Track_Control_t track_ctrl;

    // 检查当前PWM值是否超过基础速度
    bool is_consistent = (abs(speed_monitor.last_pwmA) <= track_ctrl.base_speed) &&
                        (abs(speed_monitor.last_pwmB) <= track_ctrl.base_speed);

    return is_consistent && !speed_monitor.speed_violation_detected;
}

// 重置速度监控
void Motor_Reset_Speed_Monitor(void)
{
    speed_monitor.max_pwmA_recorded = 0;
    speed_monitor.max_pwmB_recorded = 0;
    speed_monitor.speed_violation_detected = false;
    strcpy(speed_monitor.last_source, "reset");
}
